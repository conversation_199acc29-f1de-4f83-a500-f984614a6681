# 1.后端
## 导入支付宝支付依赖到pom.xml中
```
<!--引入支付宝相关依赖 -->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-easysdk</artifactId>
            <version>2.2.1</version>
        </dependency>
 
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.22.113.ALL</version>
        </dependency>
```
## alipay的java配置：AplipayConfig.java
```
package edu.scau.mis.core.config;
 
import com.alipay.easysdk.factory.Factory;
import com.alipay.easysdk.kernel.Config;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
 
import javax.annotation.PostConstruct;
 
@Data
@Component
//读取yml文件中alipay 开头的配置
@ConfigurationProperties(prefix = "alipay")
public class AliPayConfig {
    private String appId;
    private String appPrivateKey;
    private String alipayPublicKey;
    //异步通知回调地址，需要内网穿透，后面会讲
    private String notifyUrl;
    //支付成功后的回调地址，自己项目系统里的地址，随你喜欢，可以直接回到首页也行
    private String returnUrl;
 
 
    @PostConstruct
    public void init() {
        // 设置参数（全局只需设置一次）
        Config config = new Config();
        config.protocol = "https";
        config.gatewayHost = "openapi.alipaydev.com";
        config.signType = "RSA2";
        config.appId = this.appId;
        config.merchantPrivateKey = this.appPrivateKey;
        config.alipayPublicKey = this.alipayPublicKey;
        config.notifyUrl = this.notifyUrl;
        Factory.setOptions(config);
        System.out.println("=======支付宝SDK初始化成功=======");
    }
}
```
## 支付API接口（结合自己项目）
这里就涉及到自己项目里的具体情况了，其它教程很多都是新开一个controller类，然后全都塞进去，我是按照个人习惯，拆分成了service接口、serviceImpl实现类、controller路径类。

        这个地方要重点注意！我的重点实现逻辑主要放在serviceImpl，controller只是调用它，同时作为中介，把前后端参数链接。
### Alipay实体类，封装和支付宝支付有关的数据
```
package com.kp.entity;
 
import lombok.Data;
 
@Data
public class AliPay {
    private String traceNo;
    private double totalAmount;
    private String subject;
    private String alipayTraceNo;
}
```
### IAliPayService 
```
package edu.scau.mis.pos.service;
 
import edu.scau.mis.pos.domain.AliPay;
 
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
 
public interface IAliPayService {
    /**
     * 处理支付宝支付请求
     * @param aliPay 支付信息
     * @param httpResponse 响应对象
     * @throws Exception 异常
     */
    void pay(AliPay aliPay, HttpServletResponse httpResponse) throws Exception;
 
    /**
     * 处理支付宝异步回调通知
     * @param request 请求对象
     * @return 处理结果
     * @throws Exception 异常
     */
    String payNotify(HttpServletRequest request) throws Exception;
 
}
```
### AliPayServiceImpl——实现主要接入沙箱逻辑，返回订单编号、订单金额、subject给前端组成url来打开支付宝沙箱支付页面
除了通用的写支付宝沙箱页面，我这个支持在controller里获取前端传回来的数据——saleNo,订单编号，我会根据订单编号找到当前操作的订单，方便获取订单实例、订单金额，完成后更新状态。支持通过response返回给前端三个参数：subject、traceNo、totalAmount，这是用来组成支付宝沙箱页面链接的必备参数。
```
package edu.scau.mis.pos.service.impl;
 
import cn.hutool.json.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.easysdk.factory.Factory;
import edu.scau.mis.core.config.AliPayConfig;
import edu.scau.mis.pos.domain.AliPay;
import edu.scau.mis.pos.mapper.PaymentMapper;
import edu.scau.mis.pos.mapper.SaleMapper;
import edu.scau.mis.pos.service.IAliPayService;
import edu.scau.mis.pos.service.ISaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
 
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
 
@Service
public class AliPayServiceImpl implements IAliPayService {
    @Resource
    private SaleMapper saleMapper;
 
    @Resource
    private PaymentMapper paymentMapper;
 
    @Autowired
    private ISaleService saleService;
 
    @Resource
    AliPayConfig aliPayConfig;
 
    private static final String GATEWAY_URL = "https://openapi-sandbox.dl.alipaydev.com/gateway.do";
    private static final String FORMAT = "JSON";
    private static final String CHARSET = "utf-8";
    private static final String SIGN_TYPE = "RSA2";
 
    @Override
    public void pay(AliPay aliPay, HttpServletResponse httpResponse) throws Exception {
        // 1. 创建Client，通用SDK提供的Client，负责调用支付宝的API
        AlipayClient alipayClient = new DefaultAlipayClient(GATEWAY_URL, aliPayConfig.getAppId(),
                aliPayConfig.getAppPrivateKey(), FORMAT, CHARSET, aliPayConfig.getAlipayPublicKey(), SIGN_TYPE);
        // 2. 创建 Request并设置Request参数
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();  // 发送请求的 Request类
        request.setNotifyUrl(aliPayConfig.getNotifyUrl());
        System.out.println("异步回调"+request.getNotifyUrl());
        request.setReturnUrl(aliPayConfig.getReturnUrl());
        JSONObject bizContent = new JSONObject();
 
        //检查是否成功获取
        System.out.println("订单编号"+aliPay.getTraceNo());
        System.out.println("总金额"+aliPay.getTotalAmount());
        System.out.println("主题"+aliPay.getSubject());
 
        bizContent.set("out_trade_no", aliPay.getTraceNo());  // 我们自己生成的订单编号
        bizContent.set("total_amount", aliPay.getTotalAmount()); // 订单的总金额
        bizContent.set("subject", aliPay.getSubject());   // 支付的名称
        bizContent.set("product_code", "FAST_INSTANT_TRADE_PAY");  // 固定配置
        request.setBizContent(bizContent.toString());
 
        // 封装要返回给前端的信息，提供参数让前端打开带有实时订单信息的网页
        JSONObject responseData = new JSONObject();
        responseData.set("out_trade_no", aliPay.getTraceNo());
        responseData.set("total_amount", aliPay.getTotalAmount());
        responseData.set("subject", aliPay.getSubject());
 
        // 执行请求，拿到响应的结果，返回给浏览器
        String form = "";
        try {
            // 调用SDK生成表单
            form = alipayClient.pageExecute(request).getBody();
            //注意这里！和通用的不一样，我这个方法要传出resepense数据
            responseData.set("form", form);
        } catch (AlipayApiException e) {
            e.printStackTrace();
        }
        //application!不是text，因为要传回数据
        httpResponse.setContentType("application/json;charset=" + CHARSET);
        try {
 
            // 直接将完整的表单html输出到页面，注意write后的括号里的reponseData
            httpResponse.getWriter().write(responseData.toString());
            httpResponse.getWriter().flush();
            httpResponse.getWriter().close();
            System.out.println("aegrstdyju"+ responseData);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
 
    @Override
    public String payNotify(HttpServletRequest request) throws Exception {
        if (request.getParameter("trade_status").equals("TRADE_SUCCESS")) {
            System.out.println("=========支付宝异步回调========");
 
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();
            for (String name : requestParams.keySet()) {
                params.put(name, request.getParameter(name));
            }
 
            String payTime = params.get("gmt_payment");
            String alipayTradeNo = params.get("trade_no");
            String saleNo = params.get("out_trade_no");
            BigDecimal saleTotal = new BigDecimal((String) params.get("total_amount"));
            // 支付宝验签
            if (Factory.Payment.Common().verifyNotify(params)) {
                // 验签通过
                System.out.println("交易名称: " + params.get("subject"));
                System.out.println("交易状态: " + params.get("trade_status"));
                System.out.println("支付宝交易凭证号: " + params.get("trade_no"));
                System.out.println("商户订单号: " + params.get("out_trade_no"));
                System.out.println("交易金额: " + params.get("total_amount"));
                System.out.println("买家在支付宝唯一id: " + params.get("buyer_id"));
                System.out.println("买家付款时间: " + params.get("gmt_payment"));
                System.out.println("买家付款金额: " + params.get("buyer_pay_amount"));
 
                //这里！结合你的项目调用支付成功之后的处理，我是写了一个函数，完成支付后更新数据库里的订单状态，你的项目要怎么处理看你需求自己写  saleService.makeZFBPayment(saleTotal,saleMapper.selectSaleByNo(saleNo).getSaleId());
            }
        }
        return "success";
    }
}
```
### SaleController
```
package edu.scau.mis.web.controller.pos;
 
import edu.scau.mis.core.config.AliPayConfig;
import edu.scau.mis.core.domain.AjaxResult;
import edu.scau.mis.pos.domain.AliPay;
import edu.scau.mis.pos.domain.Payment;
import edu.scau.mis.pos.domain.ProductStock;
import edu.scau.mis.pos.domain.Sale;
import edu.scau.mis.pos.mapper.SaleMapper;
import edu.scau.mis.pos.service.IAliPayService;
import edu.scau.mis.pos.service.ISaleService;
import edu.scau.mis.pos.service.IStockService;
import edu.scau.mis.pos.vo.SaleItemVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
 
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
 
@RestController
@RequestMapping("sale")
public class SaleController {
    @Autowired
    private ISaleService saleService;
    @Autowired
    private IStockService stockService;
    @Autowired
    private IAliPayService aliPayService;
    @Autowired
    private SaleMapper saleMapper;
    @Resource
 
 
    /**
     * 支付宝支付
     */
    @GetMapping("/alipay") // &subject=xxx&traceNo=xxx&totalAmount=xxx
    public void aliPay(@RequestParam("saleNo") String saleNo,HttpServletResponse httpResponse) throws Exception {
//使用前端用@RequestParam("saleNo") 传回来的saleNo获取当前订单实例sale，在获取前面提到的需要的traceNo、totalAmount、subject。
        Sale sale = saleMapper.selectSaleByNo(saleNo);//自己写的方法，就是用saleNo找sale
        BigDecimal saleTotal = sale.getTotal();
        // 创建 AliPay 对象并设置相关信息
        AliPay aliPay = new AliPay();
        aliPay.setTraceNo(sale.getSaleNo()); // 使用订单编号作为交易追踪号
        aliPay.setTotalAmount(saleTotal.doubleValue());
        aliPay.setSubject("订单支付：" + sale.getSaleNo()); // 设置支付主题
        aliPayService.pay(aliPay, httpResponse);
    }
    //回调接口
    @PostMapping("/notify")  // 注意这里必须是POST接口
    public AjaxResult payNotify(HttpServletRequest request) throws Exception {
        String result = aliPayService.payNotify(request);
        if("success".equals(result)) {
            return AjaxResult.error("支付成功!");
        } else {
            return AjaxResult.success("支付失败!");
        }
    }
```
###  后端代码到这就结束啦！这里的改动的核心点就是：1.(@RequestParam("saleNo") 获取前端数据来获取当前订单实例——这个具体要看你们的项目，我是在前端有一个地方，当开始销售就会自动生成订单单号并显示在前端，结束销售之后这个订单号会有相应地sale实例写入数据库，你们可以因地制宜地有别的标识符，id之类的 ，注意数据类型就好。2.类的结构拆成了三个；3.后端会返回一个response数据而不是html表单，方便前端拿数据打开页面（其实我到现在都不懂那些写教程的是怎么打开的，很多都不给前端，大部分直接在前端写死），能灵活根据当前操作的订单数据写；4.因地制宜的支付完成处理方法
